# Melody Forge 功能架构设计

## 🎯 核心功能模块 (3个)

### 1. 🎼 快速制谱
**一站式乐谱制作解决方案**

#### 功能流程
```
图片/PDF上传 → 智能识别 → 在线编辑 → 渲染输出
```

#### 子功能模块
1. **智能乐谱识别**
   - **技术栈**: Audiveris + PaddleOCR
   - **输入格式**: JPG, PNG, PDF
   - **输出格式**: MusicXML
   - **处理流程**:
     - 图像预处理 (OpenCV)
     - Audiveris乐谱结构识别
     - PaddleOCR文本识别 (歌词、标记)
     - 结果合并生成MusicXML

2. **在线可视化编辑**
   - **技术栈**: OSMD (OpenSheetMusicDisplay)
   - **功能特性**:
     - 可视化乐谱显示
     - 音符编辑 (音高、时值、休止符)
     - 节拍修改 (拍号、调号)
     - 谱号调整 (高音谱号、低音谱号等)
     - 表情记号添加
     - 实时预览
   - **交互方式**:
     - 点击选择音符
     - 拖拽调整音高
     - 键盘快捷键操作

3. **高质量渲染输出**
   - **技术栈**: MuseScore4 + 自定义渲染引擎
   - **输出格式**:
     - PNG/JPG (高分辨率图片)
     - PDF (矢量格式)
     - MIDI (音频文件)
     - MusicXML (标准格式)
   - **渲染选项**:
     - 分辨率设置
     - 页面布局
     - 字体选择
     - 颜色主题

---

### 2. ✋ 指法标注
**智能钢琴指法标注系统**

#### 功能特性
- **输入**: MusicXML格式乐谱
- **输出**: 带指法标注的乐谱
- **算法**: 基于音程、手型、演奏习惯的智能算法

#### 核心算法
1. **指法计算引擎**
   - 分析音符序列
   - 计算手指跨度
   - 优化指法转换
   - 考虑演奏舒适度

2. **难度评估**
   - 技术难度分析
   - 手指灵活性要求
   - 速度适应性评估

3. **左右手分离处理**
   - 自动识别左右手声部
   - 分别计算最优指法
   - 协调性检查

---

### 3. 🔄 五线谱转简谱
**专业格式转换系统**

#### 转换特性
- **输入**: 五线谱 (MusicXML)
- **输出**: 中文简谱格式
- **保持**: 音乐结构、表情记号、歌词

#### 转换算法
1. **音高转换**
   - 五线谱音符 → 简谱数字
   - 调号处理
   - 八度标记

2. **节拍转换**
   - 时值对应关系
   - 拍号适配
   - 连音线处理

3. **格式化输出**
   - 中文简谱标准
   - 分行排版
   - 歌词对齐

---

## 🔄 用户使用流程

### 快速制谱流程
1. **上传乐谱图片** → 选择JPG/PNG/PDF文件
2. **智能识别** → 系统自动识别生成MusicXML
3. **在线编辑** → 使用OSMD编辑器修正错误
4. **渲染输出** → 选择格式导出成品

### 指法标注流程
1. **导入乐谱** → 上传MusicXML或使用制谱结果
2. **自动分析** → 系统计算最优指法
3. **手动调整** → 根据需要微调指法
4. **导出结果** → 获得带指法标注的乐谱

### 格式转换流程
1. **上传五线谱** → 上传MusicXML或使用制谱结果
2. **转换处理** → 系统转换为简谱格式
3. **格式调整** → 调整排版和样式
4. **导出简谱** → 获得中文简谱文件

---

## 🎨 用户界面设计

### 主页设计
- **3个大功能卡片**
- **简洁明了的功能说明**
- **一键进入各功能模块**

### 功能页面设计
- **统一的设计语言**
- **直观的操作流程**
- **实时的处理反馈**
- **便捷的结果导出**

---

## 📊 开发优先级

### Phase 1: 核心功能完善
1. ✅ 基础架构搭建
2. ✅ 外部工具集成
3. 🔄 OSMD编辑器集成
4. 🔄 指法算法实现
5. 🔄 转换算法优化

### Phase 2: 用户体验优化
1. 前端界面美化
2. 交互流程优化
3. 错误处理完善
4. 性能优化

### Phase 3: 高级功能
1. 批量处理
2. 模板系统
3. 用户偏好设置
4. 协作功能

---

**总结**: 3个核心模块，Melody Forge将提供更加聚焦和专业的音乐制谱体验，每个模块都是完整的解决方案，满足用户从识别到编辑到输出的全流程需求。
