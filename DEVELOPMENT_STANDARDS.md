# 内部开发规范文档

## 目录
- [1. 总则](#1-总则)
- [2. 配置管理](#2-配置管理)
- [3. 环境隔离](#3-环境隔离)
- [4. 代码规范](#4-代码规范)
- [5. 测试规范](#5-测试规范)
- [6. 版本控制](#6-版本控制)
- [7. 安全规范](#7-安全规范)
- [8. 部署规范](#8-部署规范)

## 1. 总则

### 1.1 目标
本规范旨在建立统一的开发标准，确保代码质量、系统稳定性和团队协作效率。

### 1.2 适用范围
适用于所有参与项目开发的团队成员，包括但不限于前端、后端、测试、运维人员。

### 1.3 强制性原则
- **配置外部化**：禁止硬编码任何配置信息
- **环境隔离**：严格区分开发、测试、生产环境
- **测试优先**：所有功能必须有对应的测试用例
- **安全第一**：敏感信息不得明文存储或传输

## 2. 配置管理

### 2.1 配置外部化原则

#### 2.1.1 禁止硬编码
**❌ 错误示例：**
```python
# 禁止硬编码API地址
api_url = "https://api.example.com/v1"
database_host = "*************"
```

**✅ 正确示例：**
```python
# 使用环境变量或配置文件
import os
api_url = os.getenv('API_URL', 'http://localhost:8000')
database_host = os.getenv('DB_HOST', 'localhost')
```

#### 2.1.2 配置文件结构
```
config/
├── development.yml
├── testing.yml
├── staging.yml
├── production.yml
└── default.yml
```

#### 2.1.3 配置优先级
1. 环境变量（最高优先级）
2. 命令行参数
3. 环境特定配置文件
4. 默认配置文件（最低优先级）

### 2.2 敏感信息管理

#### 2.2.1 使用密钥管理工具
- 生产环境：使用 AWS Secrets Manager、Azure Key Vault 等
- 开发环境：使用 .env 文件（必须加入 .gitignore）

#### 2.2.2 .env 文件示例
```bash
# .env.example - 提交到版本控制
API_URL=http://localhost:8000
DB_HOST=localhost
DB_PORT=5432
DB_NAME=myapp_dev

# .env - 不提交到版本控制
API_URL=https://api.staging.example.com
DB_HOST=staging-db.example.com
DB_PASSWORD=actual_password_here
JWT_SECRET=actual_secret_here
```

## 3. 环境隔离

### 3.1 Python 环境管理

#### 3.1.1 虚拟环境强制要求
**所有 Python 项目必须使用虚拟环境，禁止在系统环境中安装包。**

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

#### 3.1.2 依赖管理
```bash
# 生成依赖文件
pip freeze > requirements.txt

# 开发依赖单独管理
pip freeze > requirements-dev.txt
```

### 3.2 Node.js 环境管理

#### 3.2.1 版本管理
```bash
# 使用 .nvmrc 指定 Node.js 版本
echo "18.17.0" > .nvmrc

# 使用指定版本
nvm use
```

#### 3.2.2 包管理
```bash
# 优先使用 pnpm 或 yarn
pnpm install
# 或
yarn install

# 避免使用 npm（除非项目特殊要求）
```

### 3.3 Docker 环境隔离

#### 3.3.1 开发环境 Docker 配置
```dockerfile
# Dockerfile.dev
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
```

#### 3.3.2 Docker Compose 配置
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8000:8000"
    environment:
      - DEBUG=1
    volumes:
      - .:/app
    depends_on:
      - db
  
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: myapp_dev
      POSTGRES_USER: dev_user
      POSTGRES_PASSWORD: dev_password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

## 4. 代码规范

### 4.1 通用原则
- 使用有意义的变量和函数名
- 保持函数简洁，单一职责
- 添加必要的注释和文档
- 遵循语言特定的编码规范

### 4.2 Python 代码规范
- 遵循 PEP 8 标准
- 使用 black 进行代码格式化
- 使用 flake8 进行代码检查
- 使用 mypy 进行类型检查

```bash
# 安装开发工具
pip install black flake8 mypy

# 格式化代码
black .

# 代码检查
flake8 .

# 类型检查
mypy .
```

### 4.3 JavaScript/TypeScript 规范
- 使用 ESLint + Prettier
- 优先使用 TypeScript
- 遵循 Airbnb 或 Standard 编码规范

```json
// .eslintrc.json
{
  "extends": ["@typescript-eslint/recommended", "prettier"],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint"],
  "rules": {
    "no-console": "warn",
    "no-debugger": "error"
  }
}
```

## 5. 测试规范

### 5.1 测试原则
- **测试覆盖率要求**：新功能代码覆盖率不低于 80%
- **测试隔离**：每个测试用例必须独立，不依赖其他测试
- **测试清理**：测试完成后必须清理所有临时文件和数据

### 5.2 测试文件组织
```
tests/
├── unit/           # 单元测试
├── integration/    # 集成测试
├── e2e/           # 端到端测试
├── fixtures/      # 测试数据
└── conftest.py    # pytest 配置
```

### 5.3 测试清理规范

#### 5.3.1 临时文件清理
```python
import tempfile
import os
import pytest

@pytest.fixture
def temp_file():
    """创建临时文件，测试后自动清理"""
    fd, path = tempfile.mkstemp()
    yield path
    os.close(fd)
    os.unlink(path)

def test_file_processing(temp_file):
    # 使用临时文件进行测试
    with open(temp_file, 'w') as f:
        f.write("test data")
    # 测试结束后文件会自动删除
```

#### 5.3.2 数据库清理
```python
@pytest.fixture(autouse=True)
def clean_database():
    """每个测试前后清理数据库"""
    yield
    # 测试后清理
    db.session.rollback()
    for table in reversed(db.metadata.sorted_tables):
        db.session.execute(table.delete())
    db.session.commit()
```

### 5.4 测试命名规范
```python
def test_should_return_user_when_valid_id_provided():
    """测试：当提供有效ID时应该返回用户信息"""
    pass

def test_should_raise_exception_when_invalid_id_provided():
    """测试：当提供无效ID时应该抛出异常"""
    pass
```

### 5.5 测试执行规范
```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/unit/test_user.py

# 生成覆盖率报告
pytest --cov=src --cov-report=html

# 测试完成后清理覆盖率文件
rm -rf htmlcov/ .coverage
```

## 6. 版本控制

### 6.1 Git 工作流

#### 6.1.1 分支策略
- `main/master`：生产环境分支，只接受来自 `release` 分支的合并
- `develop`：开发主分支，集成所有功能分支
- `feature/*`：功能分支，从 `develop` 创建
- `release/*`：发布分支，从 `develop` 创建
- `hotfix/*`：热修复分支，从 `main` 创建

#### 6.1.2 提交规范
使用 Conventional Commits 规范：
```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型说明：**
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例：**
```
feat(auth): add JWT token validation
fix(api): resolve user registration endpoint error
docs: update API documentation
test(user): add unit tests for user service
```

### 6.2 .gitignore 规范

#### 6.2.1 通用忽略文件
```gitignore
# 环境配置
.env
.env.local
.env.*.local

# 依赖目录
node_modules/
venv/
__pycache__/
*.pyc

# 构建输出
dist/
build/
*.egg-info/

# IDE 配置
.vscode/
.idea/
*.swp
*.swo

# 日志文件
*.log
logs/

# 测试覆盖率
coverage/
htmlcov/
.coverage
.pytest_cache/

# 临时文件
*.tmp
*.temp
.DS_Store
Thumbs.db
```

### 6.3 代码审查规范

#### 6.3.1 Pull Request 要求
- 标题清晰描述变更内容
- 包含详细的变更说明
- 关联相关的 Issue 或任务
- 通过所有自动化测试
- 至少一名团队成员审查通过

#### 6.3.2 审查检查清单
- [ ] 代码符合编码规范
- [ ] 包含必要的测试用例
- [ ] 文档已更新
- [ ] 无硬编码配置
- [ ] 无安全漏洞
- [ ] 性能影响可接受

## 7. 安全规范

### 7.1 敏感信息保护

#### 7.1.1 密码和密钥管理
```python
# ❌ 错误：明文存储密码
password = "admin123"

# ✅ 正确：使用环境变量
import os
password = os.getenv('DB_PASSWORD')

# ✅ 正确：使用密钥管理服务
from azure.keyvault.secrets import SecretClient
secret = secret_client.get_secret("database-password")
```

#### 7.1.2 API 密钥保护
```python
# ❌ 错误：硬编码 API 密钥
api_key = "sk-1234567890abcdef"

# ✅ 正确：从环境变量读取
import os
api_key = os.getenv('OPENAI_API_KEY')
if not api_key:
    raise ValueError("OPENAI_API_KEY environment variable is required")
```

### 7.2 输入验证
```python
from marshmallow import Schema, fields, validate

class UserSchema(Schema):
    email = fields.Email(required=True)
    age = fields.Integer(validate=validate.Range(min=0, max=150))
    name = fields.String(validate=validate.Length(min=1, max=100))

# 验证输入数据
schema = UserSchema()
try:
    result = schema.load(request_data)
except ValidationError as err:
    return {"errors": err.messages}, 400
```

### 7.3 SQL 注入防护
```python
# ❌ 错误：字符串拼接
query = f"SELECT * FROM users WHERE id = {user_id}"

# ✅ 正确：参数化查询
query = "SELECT * FROM users WHERE id = %s"
cursor.execute(query, (user_id,))
```

## 8. 部署规范

### 8.1 环境配置

#### 8.1.1 环境变量管理
```bash
# 开发环境
export NODE_ENV=development
export DEBUG=true
export LOG_LEVEL=debug

# 生产环境
export NODE_ENV=production
export DEBUG=false
export LOG_LEVEL=info
```

#### 8.1.2 健康检查端点
```python
from flask import Flask, jsonify

@app.route('/health')
def health_check():
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': os.getenv('APP_VERSION', 'unknown')
    })
```

### 8.2 监控和日志

#### 8.2.1 结构化日志
```python
import logging
import json

class JSONFormatter(logging.Formatter):
    def format(self, record):
        log_entry = {
            'timestamp': self.formatTime(record),
            'level': record.levelname,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        return json.dumps(log_entry)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    handlers=[logging.StreamHandler()],
    format='%(message)s'
)
logger = logging.getLogger(__name__)
logger.handlers[0].setFormatter(JSONFormatter())
```

### 8.3 性能优化

#### 8.3.1 数据库连接池
```python
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

engine = create_engine(
    database_url,
    poolclass=QueuePool,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True
)
```

#### 8.3.2 缓存策略
```python
import redis
from functools import wraps

redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_result(expiration=300):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            cached_result = redis_client.get(cache_key)

            if cached_result:
                return json.loads(cached_result)

            result = func(*args, **kwargs)
            redis_client.setex(cache_key, expiration, json.dumps(result))
            return result
        return wrapper
    return decorator
```

## 9. 持续集成/持续部署 (CI/CD)

### 9.1 GitHub Actions 配置示例
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt

    - name: Run linting
      run: |
        flake8 .
        black --check .

    - name: Run tests
      run: |
        pytest --cov=src --cov-report=xml

    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml

    - name: Clean up test artifacts
      run: |
        rm -rf htmlcov/ .coverage coverage.xml
        find . -name "*.pyc" -delete
        find . -name "__pycache__" -type d -exec rm -rf {} +
```

### 9.2 部署前检查清单
- [ ] 所有测试通过
- [ ] 代码覆盖率达标
- [ ] 安全扫描通过
- [ ] 性能测试通过
- [ ] 配置文件已更新
- [ ] 数据库迁移脚本准备就绪
- [ ] 回滚方案已制定

## 10. 文档规范

### 10.1 API 文档
使用 OpenAPI/Swagger 规范：
```python
from flask_restx import Api, Resource, fields

api = Api(doc='/docs/')

user_model = api.model('User', {
    'id': fields.Integer(required=True, description='用户ID'),
    'name': fields.String(required=True, description='用户姓名'),
    'email': fields.String(required=True, description='邮箱地址')
})

@api.route('/users')
class UserList(Resource):
    @api.doc('list_users')
    @api.marshal_list_with(user_model)
    def get(self):
        """获取用户列表"""
        return users
```

### 10.2 README 模板
```markdown
# 项目名称

## 项目描述
简要描述项目的目的和功能。

## 技术栈
- Python 3.11
- Flask 2.3
- PostgreSQL 15
- Redis 7

## 快速开始

### 环境要求
- Python 3.11+
- Docker & Docker Compose

### 安装步骤
1. 克隆项目
   ```bash
   git clone <repository-url>
   cd <project-name>
   ```

2. 创建虚拟环境
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # 或
   venv\Scripts\activate     # Windows
   ```

3. 安装依赖
   ```bash
   pip install -r requirements.txt
   ```

4. 配置环境变量
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，填入实际配置
   ```

5. 运行项目
   ```bash
   python app.py
   ```

## 测试
```bash
# 运行所有测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=src --cov-report=html
```

## 部署
详见 [部署文档](docs/deployment.md)

## 贡献指南
详见 [贡献指南](CONTRIBUTING.md)
```

## 11. 违规处理

### 11.1 代码审查阶段
- 发现违规代码，审查者应要求修改后再合并
- 重复违规的开发者需要接受额外的代码规范培训

### 11.2 生产环境问题
- 因违反规范导致的生产问题，需要进行事后分析
- 制定改进措施，防止类似问题再次发生

## 12. 规范更新

### 12.1 更新流程
1. 提出规范修改建议
2. 团队讨论和评审
3. 试行期（1-2个迭代）
4. 正式发布更新

### 12.2 版本管理
- 规范文档使用语义化版本号
- 重大变更需要提前通知团队
- 保留历史版本供参考

---

**文档版本：** v1.0.0
**最后更新：** 2025-07-29
**维护者：** 开发团队
